"use client";

import { useCallback, useEffect, useRef, useState } from "react";

import { ArrowRightIcon } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import Image from "next/image";

import BlurVignette from "@/components/custom/ui/blur-vignette";

const features = [
  {
    title: "Purpose-built for product development",
    description:
      "Next Core is shaped by the practices and principles that distinguish world-class product teams from the rest: relentless focus, fast execution, and a commitment to the quality of craft.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop&crop=center",
      alt: "Product development team collaboration",
    },
  },
  {
    title: "Designed to move fast",
    description:
      "Built for speed and efficiency, Next Core helps teams ship faster with streamlined workflows and intelligent automation.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=center",
      alt: "Fast development workflow visualization",
    },
  },
  {
    title: "Crafted to perfection",
    description:
      "Every detail is carefully considered, from the interface design to the underlying architecture, ensuring a premium experience.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop&crop=center",
      alt: "Detailed interface design and architecture",
    },
  },
  {
    title: "Seamless integration",
    description:
      "Connect with your existing tools and workflows. Our platform integrates seamlessly with popular development tools and services.",
    media: {
      type: "image" as const,
      src: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=600&fit=crop&crop=center",
      alt: "Integration with development tools",
    },
  },
];

export default function FeaturesSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  // Optimized auto-scroll with intersection observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { threshold: 0.3 },
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Auto-scroll functionality - only when in view
  useEffect(() => {
    if (!isPlaying || hasUserInteracted || !isInView) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    intervalRef.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % features.length);
    }, 4000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isPlaying, hasUserInteracted, isInView]);

  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
    setHasUserInteracted(true);
    setIsPlaying(false);
  }, []);

  return (
    <section
      ref={sectionRef}
      className="bg-muted/30 relative px-4 py-24 sm:px-6 lg:px-8"
    >
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <div className="mb-16 grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            className="space-y-4"
          >
            <p className="text-muted-foreground text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
            <div className="flex items-center">
              <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
                Make the switch <ArrowRightIcon className="h-3 w-3" />
              </span>
            </div>
          </motion.div>
        </div>

        {/* Auto-scrolling Feature Showcase - Linear Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="grid grid-cols-1 gap-8 lg:grid-cols-2 lg:gap-6"
        >
          {/* Features List - Left Side */}
          <div className="space-y-2">
            {features.map((feature, index) => {
              const isActive = index === currentIndex;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, amount: 0.1 }}
                  transition={{
                    duration: 0.6,
                    ease: "easeOut",
                    delay: index * 0.1,
                  }}
                  className={`group relative min-h-[200px] cursor-pointer rounded-r-md p-6 transition-all duration-300 will-change-transform ${
                    isActive ? "bg-card dark:bg-card/50" : ""
                  }`}
                  onClick={() => goToSlide(index)}
                >
                  {/* Step Number */}
                  <div className="mb-4 flex items-center gap-3">
                    <div
                      className={`flex h-6 w-6 items-center justify-center rounded-full text-xs font-medium transition-colors duration-300 ${
                        isActive
                          ? "bg-sky-300 text-white dark:bg-sky-700"
                          : "bg-muted-foreground/20 text-muted-foreground"
                      }`}
                    >
                      {index + 1}
                    </div>
                  </div>

                  {/* Feature Content */}
                  <h3
                    className={`mb-3 text-lg leading-tight font-semibold transition-colors duration-300 ${
                      isActive
                        ? "text-foreground"
                        : "text-muted-foreground group-hover:text-foreground"
                    }`}
                  >
                    {feature.title}
                  </h3>
                  <p
                    className={`text-sm leading-relaxed transition-colors duration-300 ${
                      isActive
                        ? "text-muted-foreground"
                        : "text-muted-foreground/70 group-hover:text-muted-foreground"
                    }`}
                  >
                    {feature.description}
                  </p>

                  {/* Active Indicator */}
                  {isActive && (
                    <motion.div
                      layoutId="activeFeature"
                      className="absolute top-0 left-0 h-full w-0.5 rounded-l-xl bg-sky-300 dark:bg-sky-700"
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                    />
                  )}
                </motion.div>
              );
            })}
          </div>

          {/* Media Display - Right Side */}
          <div className="relative">
            <div className="sticky top-24 overflow-hidden rounded-lg">
              <BlurVignette
                className="bg-muted/20 relative aspect-[4/3]"
                radius="12px"
                inset="20px"
                transitionLength="40px"
                blur="24px"
              >
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentIndex}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{
                      duration: 0.5,
                      ease: [0.25, 0.46, 0.45, 0.94],
                      opacity: { duration: 0.3 },
                    }}
                    className="absolute inset-0 will-change-transform"
                  >
                    {features[currentIndex].media.type === "image" ? (
                      <Image
                        src={features[currentIndex].media.src}
                        alt={features[currentIndex].media.alt}
                        fill
                        className="rounded-lg border-2 object-cover transition-transform duration-300 hover:scale-105"
                        priority={currentIndex === 0}
                        sizes="(max-width: 768px) 100vw, 50vw"
                        quality={85}
                      />
                    ) : (
                      <video
                        src={features[currentIndex].media.src}
                        autoPlay
                        muted
                        loop
                        playsInline
                        className="h-full w-full rounded-lg object-cover"
                      />
                    )}
                  </motion.div>
                </AnimatePresence>
              </BlurVignette>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
