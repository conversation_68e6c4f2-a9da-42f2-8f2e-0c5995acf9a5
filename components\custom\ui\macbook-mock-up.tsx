import Image from "next/image";

import { cn } from "@/lib/utils";

export function MacbookMockUp({
  src,
  className,
  children,
}: Readonly<{
  src: string;
  className?: string;
  children?: React.ReactNode;
}>) {
  return (
    <div
      className={cn("relative z-1 mx-0 my-4 w-full max-w-[740px]", className)}
      style={{ aspectRatio: "740/434" }}
    >
      <div
        className="relative z-1 mx-auto my-0 overflow-hidden rounded-[20px] border-2 border-[rgb(200,202,203)] [background:rgb(13,13,13)]"
        style={{
          width: "83.5%", // 618/740
          height: "96.3%", // 418/434
          paddingLeft: "1.2%", // 9/740
          paddingRight: "1.2%", // 9/740
          paddingTop: "2.1%", // 9/434
          paddingBottom: "5.3%", // 23/434
        }}
      >
        {children || (
          <Image
            src={src}
            fill
            alt="Macbook Pro background"
            className="relative w-full rounded-t-[20px] border-2 border-solid border-[rgb(18,18,18)] bg-amber-200 bg-cover object-cover"
            loading="eager"
            priority
          />
        )}
        <div
          className="absolute right-0 bottom-0 left-0 bg-linear-to-b from-[#272727] to-[#0d0d0d]"
          style={{ height: "5.7%" }} // 24/418
        />
      </div>

      {/* Camera notch */}
      <div
        className="absolute z-2 rounded-br rounded-bl bg-[rgb(13,13,13)]"
        style={{
          top: "2.5%", // 11/434
          left: "50%",
          transform: "translateX(-50%)",
          height: "3%", // 13/434
          width: "8.6%", // 64/740
        }}
      />

      {/* Base/stand */}
      <div
        className="relative z-9 rounded-[2px_2px_12px_12px] border-[1px_2px_0px] border-solid border-[rgb(160,163,167)] shadow-[rgb(108,112,116)_0px_-2px_8px_0px_inset] [background:radial-gradient(circle,rgb(226,227,228)_85%,rgb(200,202,203)_100%)] [border-image:initial]"
        style={{
          marginTop: "-2.3%", // -10/434
          height: "5.5%", // 24/434
          width: "100%",
        }}
      >
        <div
          className="absolute top-0 left-1/2 rounded-b-[10px] shadow-[inset_0_0_4px_2px_#babdbf]"
          style={{
            transform: "translateX(-50%)",
            height: "41.7%", // 10/24
            width: "16.2%", // 120/740
          }}
        />
      </div>

      {/* Feet */}
      <div
        className="absolute rounded-b-full bg-neutral-600"
        style={{
          bottom: "-0.1%", // -0.5/434
          left: "6.5%", // 48/740
          height: "0.1%", // 0.5/434
          width: "5.4%", // 40/740
        }}
      />
      <div
        className="absolute rounded-b-full bg-neutral-600"
        style={{
          bottom: "-0.1%", // -0.5/434
          right: "6.5%", // 48/740
          height: "0.1%", // 0.5/434
          width: "5.4%", // 40/740
        }}
      />
    </div>
  );
}
